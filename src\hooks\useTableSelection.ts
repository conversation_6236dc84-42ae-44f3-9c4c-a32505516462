import { useState, useCallback } from "react";
import type { DataItem, SelectionState } from "../types/user";

/**
 * 表格选择管理Hook
 * 提供多选、清除选择等功能
 */
export const useTableSelection = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<DataItem[]>([]);

  /**
   * 处理多选变化（支持跨页选择）
   */
  const handleSelectionChange = useCallback(
    (keys: React.Key[], rows: DataItem[]) => {
      setSelectedRowKeys(keys);
      setSelectedRows(rows);
      console.log("选中的行数据:", rows);
    },
    [],
  );

  /**
   * 取消所有选择
   */
  const clearSelection = useCallback(() => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  }, []);

  /**
   * 检查是否有选中项
   */
  const hasSelection = selectedRowKeys.length > 0;

  /**
   * 获取选中项数量
   */
  const selectionCount = selectedRowKeys.length;

  /**
   * 获取选择状态
   */
  const selectionState: SelectionState = {
    selectedRowKeys,
    selectedRows,
  };

  return {
    selectedRowKeys,
    selectedRows,
    handleSelectionChange,
    clearSelection,
    hasSelection,
    selectionCount,
    selectionState,
  };
};

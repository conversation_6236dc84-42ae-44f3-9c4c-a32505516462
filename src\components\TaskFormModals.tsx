import React, { useEffect } from 'react';
import { Modal, Form, Input, Select, Switch, Row, Col, Table, Button, Space, InputNumber, Tag } from 'antd';
import type { TaskAlert, DBConnection, AlertSend, OtherInfo } from '../types/task';
import {
  DB_TYPE_OPTIONS,
  ALERT_TYPE_OPTIONS,
  ALERT_SEVERITY_OPTIONS,
  SEND_TYPE_OPTIONS,
  ORACLE_CONNECT_METHOD_OPTIONS,
} from '../types/task';

const { Option } = Select;
const { TextArea } = Input;

interface AlertModalProps {
  visible: boolean;
  editingData?: TaskAlert;
  onCancel: () => void;
  onSubmit: (data: TaskAlert) => void;
}

interface DbConnectionModalProps {
  visible: boolean;
  editingData?: DBConnection;
  onCancel: () => void;
  onSubmit: (data: DBConnection) => void;
}

interface AlertSendModalProps {
  visible: boolean;
  editingData?: AlertSend;
  onCancel: () => void;
  onSubmit: (data: AlertSend) => void;
}

interface OtherInfoModalProps {
  visible: boolean;
  editingData?: OtherInfo;
  onCancel: () => void;
  onSubmit: (data: OtherInfo) => void;
}

interface SelectModalProps {
  visible: boolean;
  type: 'alert' | 'alertSend' | 'dbConnection' | 'otherInfo';
  data: any[];
  onCancel: () => void;
  onSubmit: (selectedItems: any[]) => void;
  multiple?: boolean;
}

// 告警Modal
export const AlertModal: React.FC<AlertModalProps> = ({ visible, editingData, onCancel, onSubmit }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const alertData: TaskAlert = {
      id: editingData?.id || Date.now(),
      ...values,
      values: values.type === 'isEqual' ? values.values || [] : [],
    };
    onSubmit(alertData);
  };

  return (
    <Modal title={editingData ? '编辑告警规则' : '新增告警规则'} open={visible} onCancel={onCancel} footer={null} width={600}>
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='告警名称'
              name='name'
              rules={[
                {
                  required: true,
                  message: '请输入告警名称',
                },
              ]}
            >
              <Input placeholder='请输入告警名称' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='告警级别'
              name='severity'
              rules={[
                {
                  required: true,
                  message: '请选择告警级别',
                },
              ]}
            >
              <Select placeholder='请选择告警级别'>
                {ALERT_SEVERITY_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label='告警类型'
          name='type'
          rules={[
            {
              required: true,
              message: '请选择告警类型',
            },
          ]}
        >
          <Select placeholder='请选择告警类型'>
            {ALERT_TYPE_OPTIONS.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label='SQL语句'
          name='sql'
          rules={[
            {
              required: true,
              message: '请输入SQL语句',
            },
          ]}
        >
          <TextArea rows={4} placeholder='请输入SQL语句' />
        </Form.Item>

        <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}>
          {({ getFieldValue }) =>
            getFieldValue('type') === 'isEqual' ? (
              <Form.Item
                label='触发值'
                name='values'
                rules={[
                  {
                    required: true,
                    message: '请输入触发值',
                  },
                ]}
              >
                <Select
                  mode='tags'
                  placeholder='请输入触发值，支持多个值'
                  style={{
                    width: '100%',
                  }}
                />
              </Form.Item>
            ) : null
          }
        </Form.Item>

        <div className='flex justify-end space-x-3 pt-4 border-t border-gray-200'>
          <Button onClick={onCancel}>取消</Button>
          <Button type='primary' htmlType='submit'>
            保存
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 数据库连接Modal
export const DbConnectionModal: React.FC<DbConnectionModalProps> = ({ visible, editingData, onCancel, onSubmit }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const dbData: DBConnection = {
      id: editingData?.id || Date.now(),
      ...values,
    };
    onSubmit(dbData);
  };

  return (
    <Modal title={editingData ? '编辑数据库连接' : '新增数据库连接'} open={visible} onCancel={onCancel} footer={null} width={800}>
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='连接名称'
              name='name'
              rules={[
                {
                  required: true,
                  message: '请输入连接名称',
                },
              ]}
            >
              <Input placeholder='请输入连接名称' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='数据库类型'
              name='db_type'
              rules={[
                {
                  required: true,
                  message: '请选择数据库类型',
                },
              ]}
            >
              <Select placeholder='请选择数据库类型'>
                {DB_TYPE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={16}>
            <Form.Item
              label='主机地址'
              name='host'
              rules={[
                {
                  required: true,
                  message: '请输入主机地址',
                },
              ]}
            >
              <Input placeholder='请输入主机地址' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label='端口'
              name='port'
              rules={[
                {
                  required: true,
                  message: '请输入端口',
                },
              ]}
            >
              <Input placeholder='请输入端口' />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='用户名'
              name='user'
              rules={[
                {
                  required: true,
                  message: '请输入用户名',
                },
              ]}
            >
              <Input placeholder='请输入用户名' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='密码'
              name='passwd'
              rules={[
                {
                  required: true,
                  message: '请输入密码',
                },
              ]}
            >
              <Input.Password placeholder='请输入密码' />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.db_type !== currentValues.db_type}>
          {({ getFieldValue }) =>
            getFieldValue('db_type') === 'mysql' ? (
              <>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label='数据库名'
                      name='database'
                      rules={[
                        {
                          required: true,
                          message: '请输入数据库名',
                        },
                      ]}
                    >
                      <Input placeholder='请输入数据库名' />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label='服务器时区' name='serverTimezone' initialValue='Asia/Shanghai'>
                      <Input placeholder='请输入服务器时区' />
                    </Form.Item>
                  </Col>
                </Row>
                <Form.Item label='使用SSL' name='useSSL' valuePropName='checked' initialValue={false}>
                  <Switch />
                </Form.Item>
              </>
            ) : getFieldValue('db_type') === 'oracle' ? (
              <>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label='实例名'
                      name='instance'
                      rules={[
                        {
                          required: true,
                          message: '请输入实例名',
                        },
                      ]}
                    >
                      <Input placeholder='请输入实例名' />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label='连接方式'
                      name='connectMethod'
                      rules={[
                        {
                          required: true,
                          message: '请选择连接方式',
                        },
                      ]}
                    >
                      <Select placeholder='请选择连接方式'>
                        {ORACLE_CONNECT_METHOD_OPTIONS.map(option => (
                          <Option key={option.value} value={option.value}>
                            {option.label}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </>
            ) : null
          }
        </Form.Item>

        <div className='flex justify-end space-x-3 pt-4 border-t border-gray-200'>
          <Button onClick={onCancel}>取消</Button>
          <Button type='primary' htmlType='submit'>
            保存
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 告警发送Modal
export const AlertSendModal: React.FC<AlertSendModalProps> = ({ visible, editingData, onCancel, onSubmit }) => {
  const [form] = Form.useForm();
  const [sendType, setSendType] = useState<string>('');

  useEffect(() => {
    if (visible && editingData) {
      // 编辑模式：设置表单值
      const formValues = {
        name: editingData.name,
        type: editingData.type,
        // Kafka 配置
        kafka_address: editingData.kafka_receiver?.address || '',
        kafka_username: editingData.kafka_receiver?.username || '',
        kafka_password: editingData.kafka_receiver?.password || '',
        kafka_topic: editingData.kafka_receiver?.topic || '',
        // Prometheus 配置
        prometheus_address: editingData.prometheus_receiver?.address || '',
        prometheus_username: editingData.prometheus_receiver?.username || '',
        prometheus_password: editingData.prometheus_receiver?.password || '',
      };
      form.setFieldsValue(formValues);
      setSendType(editingData.type);
    } else if (visible) {
      // 新增模式：重置表单
      form.resetFields();
      setSendType('');
    }
  }, [visible, editingData, form]);

  const handleTypeChange = (value: string) => {
    setSendType(value);
    // 清空其他类型的字段
    if (value === 'kafka') {
      form.setFieldsValue({
        prometheus_address: '',
        prometheus_username: '',
        prometheus_password: '',
      });
    } else if (value === 'prometheus') {
      form.setFieldsValue({
        kafka_address: '',
        kafka_username: '',
        kafka_password: '',
        kafka_topic: '',
      });
    }
  };

  const handleSubmit = async (values: any) => {
    const alertSendData: AlertSend = {
      id: editingData?.id || Date.now(),
      name: values.name,
      type: values.type,
      kafka_receiver: {
        address: values.type === 'kafka' ? values.kafka_address : '',
        username: values.type === 'kafka' ? values.kafka_username : '',
        password: values.type === 'kafka' ? values.kafka_password : '',
        topic: values.type === 'kafka' ? values.kafka_topic : '',
      },
      prometheus_receiver: {
        address: values.type === 'prometheus' ? values.prometheus_address : '',
        username: values.type === 'prometheus' ? values.prometheus_username : '',
        password: values.type === 'prometheus' ? values.prometheus_password : '',
      },
      create_time: editingData?.create_time || new Date().toISOString().slice(0, 19).replace('T', ' '),
      update_time: new Date().toISOString().slice(0, 19).replace('T', ' '),
    };
    onSubmit(alertSendData);
  };

  return (
    <Modal title={editingData ? '编辑告警发送' : '新增告警发送'} open={visible} onCancel={onCancel} footer={null} width={700}>
      <Form form={form} layout='vertical' onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='发送名称'
              name='name'
              rules={[
                {
                  required: true,
                  message: '请输入发送名称',
                },
              ]}
            >
              <Input placeholder='请输入发送名称' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='发送类型'
              name='type'
              rules={[
                {
                  required: true,
                  message: '请选择发送类型',
                },
              ]}
            >
              <Select placeholder='请选择发送类型' onChange={handleTypeChange}>
                {SEND_TYPE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* Kafka 配置 */}
        {sendType === 'kafka' && (
          <div className='bg-blue-50 p-4 rounded-lg mb-4'>
            <h4 className='text-blue-800 font-medium mb-3'>Kafka 配置</h4>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label='Kafka地址'
                  name='kafka_address'
                  rules={[
                    {
                      required: true,
                      message: '请输入Kafka地址',
                    },
                  ]}
                  extra='格式：*************:9092,*************:9092'
                >
                  <Input placeholder='请输入Kafka地址，多个地址用逗号分隔' />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label='用户名'
                  name='kafka_username'
                  rules={[
                    {
                      required: true,
                      message: '请输入用户名',
                    },
                  ]}
                >
                  <Input placeholder='请输入用户名' />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label='密码'
                  name='kafka_password'
                  rules={[
                    {
                      required: true,
                      message: '请输入密码',
                    },
                  ]}
                >
                  <Input.Password placeholder='请输入密码' />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label='Topic'
                  name='kafka_topic'
                  rules={[
                    {
                      required: true,
                      message: '请输入Topic',
                    },
                  ]}
                >
                  <Input placeholder='请输入Topic' />
                </Form.Item>
              </Col>
            </Row>
          </div>
        )}

        {/* Prometheus 配置 */}
        {sendType === 'prometheus' && (
          <div className='bg-green-50 p-4 rounded-lg mb-4'>
            <h4 className='text-green-800 font-medium mb-3'>Prometheus 配置</h4>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  label='Prometheus地址'
                  name='prometheus_address'
                  rules={[
                    {
                      required: true,
                      message: '请输入Prometheus地址',
                    },
                  ]}
                  extra='格式：*************:9090,*************:9090'
                >
                  <Input placeholder='请输入Prometheus地址，多个地址用逗号分隔' />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label='用户名'
                  name='prometheus_username'
                  rules={[
                    {
                      required: true,
                      message: '请输入用户名',
                    },
                  ]}
                >
                  <Input placeholder='请输入用户名' />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label='密码'
                  name='prometheus_password'
                  rules={[
                    {
                      required: true,
                      message: '请输入密码',
                    },
                  ]}
                >
                  <Input.Password placeholder='请输入密码' />
                </Form.Item>
              </Col>
            </Row>
          </div>
        )}

        <div className='flex justify-end space-x-3 pt-4 border-t border-gray-200'>
          <Button onClick={onCancel}>取消</Button>
          <Button type='primary' htmlType='submit'>
            {editingData ? '更新' : '保存'}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

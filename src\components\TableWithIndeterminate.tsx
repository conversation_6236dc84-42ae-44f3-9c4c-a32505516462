import { useRef, useState } from "react";
import { ListTable } from "@visactor/react-vtable";

// 定义类型
interface TableRecord {
  id: number;
  name: string;
  age: number;
  gender: string;
}

function TableWithIndeterminate() {
  const tableRef = useRef(null);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checked, setChecked] = useState(false);

  const records: TableRecord[] = [
    {
      id: 1,
      name: "<PERSON>",
      age: 28,
      gender: "Male",
    },
    {
      id: 2,
      name: "<PERSON>",
      age: 32,
      gender: "Female",
    },
    {
      id: 3,
      name: "<PERSON>",
      age: 45,
      gender: "Male",
    },
  ];

  const columns = [
    {
      field: "checkbox",
      //   title: "选择",
      width: 80,
      headerType: "checkbox",
      cellType: "checkbox",
      headerStyle: {
        indeterminate: indeterminate, // 控制表头复选框的 indeterminate 状态
      },
      style: { textAlign: "center" },
    },
    {
      field: "name",
      title: "姓名",
      width: 150,
    },
    {
      field: "age",
      title: "年龄",
      width: 100,
    },
    {
      field: "gender",
      title: "性别",
      width: 100,
    },
  ];

  const option = {
    records,
    columns,
    checkbox: {
      checked: checked,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      onChange: (_state: any, _col: any, _row: any, table: any) => {
        updateHeaderCheckboxState(table);
      },
    },
  };

  // 更新表头复选框状态
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const updateHeaderCheckboxState = (table: any) => {
    const selectedCount = table.getCheckedRecords().length;
    const totalCount = table.recordsCount;

    if (selectedCount === 0) {
      setChecked(false);
      setIndeterminate(false);
    } else if (selectedCount === totalCount) {
      setChecked(true);
      setIndeterminate(false);
    } else {
      setChecked(false);
      setIndeterminate(true);
    }
  };

  return (
    <div
      style={{
        width: "100%",
        height: "500px",
      }}
    >
      {/* eslint-disable-next-line @typescript-eslint/no-explicit-any */}
      <ListTable ref={tableRef} option={option as any} />
    </div>
  );
}

export default TableWithIndeterminate;

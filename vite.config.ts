import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";
import AutoImport from "unplugin-auto-import/vite";
import path from "path";
import { fileURLToPath } from "url";

// Get __dirname equivalent in ES modules
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),

    // 自动导入 API 和组件
    AutoImport({
      imports: [
        "react",
        "react-router-dom",
        {
          // 从 antd 自动导入组件
          antd: [
            // "Button",
            // "Card",
            "Table",
            "ProTable",
            // 添加更多 antd 组件
          ],
        },
      ],
      dts: "src/auto-imports.d.ts", // 生成类型声明文件
      eslintrc: {
        enabled: true, // 生成 ESLint 配置
        filepath: "./.eslintrc-auto-import.json",
      },
      // 包含的文件类型
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
      ],
      // 排除的目录
      exclude: ["**/node_modules/**", "**/dist/**", "**/.git/**"],
    }),
  ],
  server: {
    host: "0.0.0.0", // 允许任何网卡访问
    port: 5173, // 指定端口号（可选）
    strictPort: false, // 如果端口被占用，自动尝试下一个可用端口
    open: false, // 自动打开浏览器（可选）
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"), // 将 @ 映射到 src 目录
    },
  },
});

import React from "react";
import { Space, Popconfirm } from "antd";
import type { DataItem } from "../../types/user";

/**
 * 用户操作组件Props
 */
interface UserActionsProps {
  // 数据
  record: DataItem;
  onEdit: (record: DataItem) => void;
  onDelete: (record: DataItem) => void;
  onDeleteWithPopconfirm?: boolean;
  loading?: boolean;
}

/**
 * 用户操作组件
 * 提供编辑和删除操作
 */
export const UserActions: React.FC<UserActionsProps> = ({
  record,
  onEdit,
  onDelete,
  onDeleteWithPopconfirm,
  loading = false,
}) => {
  if (onDeleteWithPopconfirm) {
    // 需要包裹Popconfirm时
    return (
      <Space size="middle">
        <a
          onClick={() => onEdit(record)}
          style={{
            color: loading ? "#ccc" : undefined,
            pointerEvents: loading ? "none" : "auto",
          }}
        >
          编辑
        </a>

        <Popconfirm
          title="确定要删除吗？"
          description={`确定要删除用户 "${record.name}" 吗？此操作不可恢复。`}
          onConfirm={() => onDelete(record)}
          okText="确定"
          cancelText="取消"
          okType="danger"
          disabled={loading}
        >
          <a
            style={{
              color: loading ? "#ccc" : "red",
              pointerEvents: loading ? "none" : "auto",
            }}
          >
            删除
          </a>
        </Popconfirm>
      </Space>
    );
  } else {
    // 不需要包裹Popconfirm时，之后调用Modal确认
    return (
      <Space size="middle">
        <a
          onClick={() => onEdit(record)}
          style={{
            color: loading ? "#ccc" : undefined,
            pointerEvents: loading ? "none" : "auto",
          }}
        >
          编辑
        </a>
        <a
          onClick={() => onDelete(record)}
          style={{
            color: loading ? "#ccc" : "red",
            pointerEvents: loading ? "none" : "auto",
          }}
        >
          删除
        </a>
      </Space>
    );
  }
};

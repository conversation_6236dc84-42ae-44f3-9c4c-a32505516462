# 任务管理表格组件

这是一个使用 React + Ant Design 实现的功能完整的任务管理表格组件，包含了查询、新增、编辑、删除、分页等完整功能。

## 功能特性

### ✅ 已实现的功能

1. **查询功能**
   - 顶部快速查询：任务名称、任务分组、任务状态
   - 重置查询：一键清空所有搜索条件并重新加载数据
   - 详细查询：Modal 弹窗包含所有字段的高级查询
   - 实时搜索和过滤

2. **表格核心功能**
   - 数据展示：1000 条模拟任务数据
   - 多选功能：支持单选和全选
   - 列固定：任务名称固定左侧，操作列固定右侧
   - 操作列：编辑和删除按钮，宽度优化为 140px
   - 响应式布局：表格自适应屏幕大小

3. **编辑功能**
   - 抽屉表单：右侧滑出的编辑表单
   - 关闭确认：表单有修改时提示确认关闭
   - 表单验证：完整的字段验证规则

4. **删除功能**
   - 单个删除：Popconfirm 确认删除
   - 批量删除：Modal 确认批量删除
   - 删除后自动刷新数据

5. **新增功能**
   - Modal 表单：弹窗形式的新增表单
   - 表单验证：完整的字段验证规则
   - 新增后自动刷新数据

6. **分页功能**
   - 底部固定分页
   - 显示数据总量
   - 支持跳转到指定页面
   - 支持修改每页显示数量
   - 分页切换正确触发数据加载

7. **多选批量操作**
   - 选中项时显示批量操作栏
   - 批量删除功能
   - 取消全选功能
   - 操作栏绝对定位覆盖在查询区域之上，不挤压下方容器

8. **布局优化**
   - 查询区域固定在顶部
   - 分页区域固定在底部
   - 表格区域中间可滚动
   - 响应式设计

## 技术栈

- **React 18** - 前端框架
- **TypeScript** - 类型安全
- **Ant Design 5** - UI 组件库
- **Tailwind CSS** - 样式框架

## 文件结构

```
src/
├── types/task.ts              # 任务数据类型定义
├── services/taskService.ts   # 任务数据服务（模拟API）
├── components/AntdTable.tsx  # 主要的表格组件
└── pages/TaskTablePage.tsx   # 任务管理页面
```

## 数据模型

```typescript
interface TaskData {
  id: number;
  name: string; // 任务名称
  group: string; // 任务分组
  start_time: string; // 开始时间 (HH:mm:ss)
  end_time: string; // 结束时间 (HH:mm:ss)
  weekday: string; // 星期 (1-7 或 1,2,3,4,5)
  frequency: string; // 执行频率
  retryNum: string; // 重试次数
  retry_frequency: string; // 重试间隔
  alert_trigger: string; // 告警触发条件
  alert_receiver: string; // 告警接收人
  createTime: string; // 创建时间
  status: "active" | "inactive" | "pending"; // 状态
}
```

## 使用方法

### 1. 访问页面

```
http://localhost:5174/task-management
```

### 2. 在路由中使用

```typescript
import TaskTablePage from './pages/TaskTablePage';

// 在路由配置中添加
{
  path: "/task-management",
  Component: () => <TaskTablePage />,
}
```

### 3. 直接使用组件

```typescript
import AntdTable from "./components/AntdTable";

function App() {
  return <AntdTable />;
}
```

## API 接口

组件使用模拟的 API 服务，包含以下接口：

- `getTasks(params)` - 获取任务列表（支持分页和搜索）
- `addTask(taskData)` - 新增任务
- `updateTask(id, taskData)` - 更新任务
- `deleteTask(id)` - 删除单个任务
- `batchDeleteTasks(ids)` - 批量删除任务

## 特色功能

1. **智能布局**：查询固定顶部，分页固定底部，表格中间滚动
2. **交互优化**：编辑抽屉关闭确认，批量操作覆盖显示
3. **数据管理**：1000 条模拟数据，支持完整的 CRUD 操作
4. **响应式设计**：适配不同屏幕尺寸
5. **类型安全**：完整的 TypeScript 类型定义

## 最新优化 (2025-01-05)

### 🔧 修复和优化项目

1. **新增重置查询按钮** - 一键清空所有搜索条件并重新加载数据
2. **任务名称列固定** - 任务名称列固定在表格左侧，水平滚动时始终可见
3. **操作列优化** - 操作列固定在右侧，宽度调整为 140px，确保按钮完整显示
4. **分页功能修复** - 修复分页切换不生效的问题，确保分页变化时正确加载数据
5. **多选提示框优化** - 批量操作栏使用绝对定位覆盖在其他容器之上，不再挤压下方容器

### 🎯 技术改进

- 优化了 `loadData` 函数，支持传入自定义参数
- 修复了分页状态更新的异步问题
- 改进了批量操作栏的 CSS 定位策略
- 增强了 TypeScript 类型安全性

## 开发说明

这个组件展示了如何使用 Ant Design 构建一个功能完整的数据管理界面，包含了企业级应用中常见的所有功能需求。代码结构清晰，易于维护和扩展。经过最新优化，用户体验和功能稳定性得到显著提升。
